# Makefile RPM 构建命令总结

## 添加的内容

我已经为您的 `kunpeng-tap.spec` 文件和 `Makefile` 添加了完整的 RPM 构建支持。

### 1. 更新的文件

#### kunpeng-tap.spec
- 添加了 `BuildRequires` 依赖项（golang, make, git）
- 更新了 `%build` 部分，使用 `make build-koord-runtime-proxy` 命令
- 修正了版本和发布号的变量引用
- 设置了正确的 Go 环境变量

#### Makefile
- 添加了完整的 RPM 构建命令集
- 包含了 15 个新的 make 目标

### 2. 新增的 Make 命令

#### 基础命令
- `make rpm-help` - 显示所有 RPM 相关命令的帮助信息
- `make rpm-info` - 显示当前 RPM 包配置信息
- `make rpm-prepare` - 准备 RPM 构建环境

#### 构建命令
- `make rpm-source` - 创建源码 tarball
- `make rpm-spec` - 复制 spec 文件到构建目录
- `make rpm-build` - 构建 RPM 包
- `make rpm-build-docker` - 构建 Docker 运行时版本的 RPM 包
- `make rpm-build-containerd` - 构建 Containerd 运行时版本的 RPM 包

#### 验证和测试命令
- `make rpm-verify` - 验证构建的 RPM 包
- `make rpm-test-install` - 测试安装 RPM 包（干运行）

#### 安装和卸载命令
- `make rpm-install` - 安装构建的 RPM 包
- `make rpm-uninstall` - 卸载 RPM 包

#### 维护命令
- `make rpm-clean` - 清理 RPM 构建文件
- `make rpm-all` - 完整的构建和验证流程

### 3. 可配置的变量

- `RUNTIME_TYPE` - 容器运行时类型（docker/containerd）
- `RPM_VERSION` - RPM 包版本号
- `RPM_RELEASE` - RPM 包发布号
- `RPM_ARCH` - 目标架构（默认 aarch64）

### 4. 使用示例

#### 快速构建 Docker 版本
```bash
make rpm-build-docker
```

#### 快速构建 Containerd 版本
```bash
make rpm-build-containerd
```

#### 完整的构建和验证流程
```bash
make rpm-all
```

#### 自定义版本构建
```bash
make rpm-build RPM_VERSION=1.2.3 RPM_RELEASE=2 RUNTIME_TYPE=containerd
```

### 5. 构建流程

1. **准备环境**: `make rpm-prepare`
2. **构建二进制**: 自动调用 `make build-koord-runtime-proxy`
3. **创建源码包**: 打包所有必要文件
4. **复制 spec 文件**: 到 RPM 构建目录
5. **执行 rpmbuild**: 使用正确的参数构建 RPM
6. **验证包**: 检查包内容和依赖

### 6. 输出位置

构建成功后，RPM 包将位于：
```
rpmbuild/RPMS/aarch64/kunpeng-tap-<version>-<release>.aarch64.rpm
```

### 7. 包内容

- `/usr/local/bin/kunpeng-tap` - 主要二进制文件
- `/etc/systemd/system/kunpeng-tap.service` - systemd 服务文件
- `/var/run/kunpeng/` - 运行时目录

### 8. 自动化功能

- 自动检测 Git 版本作为 RPM 版本
- 根据运行时类型选择正确的 systemd 服务文件
- 自动处理依赖关系
- 包含完整的安装/卸载脚本

### 9. 测试验证

所有命令都已经测试过，可以正常工作：
- ✅ `make rpm-help` - 显示帮助信息
- ✅ `make rpm-info` - 显示包信息
- ✅ `make rpm-prepare` - 创建构建目录

您现在可以使用这些命令来构建和管理您的 kunpeng-tap RPM 包了！
