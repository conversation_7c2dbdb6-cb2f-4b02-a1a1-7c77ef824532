version: '1.0'
name: build-20250123new
displayName: build
triggers:
  trigger: auto
  pr:
    branches:
      prefix:
        - ''
stages:
  - name: stage-c514c293
    displayName: 未命名
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@golang
        name: build_golang
        displayName: Golang 构建
        golangVersion: '1.22'
        commands:
          - '# 默认使用goproxy.cn'
          - export GOPROXY=https://goproxy.cn
          - '# 输入你的构建命令'
          - make build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./bin/manager
        caches:
          - /go/pkg/mod
        notify: []
        strategy:
          retry: '0'
strategy:
  blocking: true
