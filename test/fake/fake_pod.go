// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"sync"

	v1 "k8s.io/api/core/v1"
	"kunpeng.huawei.com/tap/api/policy-manager/v1alpha1"
	"kunpeng.huawei.com/tap/pkg/cache"
)

type FakePod struct {
	GetAnnotationsStub        func() map[string]string
	getAnnotationsMutex       sync.RWMutex
	getAnnotationsArgsForCall []struct {
	}
	getAnnotationsReturns struct {
		result1 map[string]string
	}
	getAnnotationsReturnsOnCall map[int]struct {
		result1 map[string]string
	}
	GetCgroupParentDirStub        func() string
	getCgroupParentDirMutex       sync.RWMutex
	getCgroupParentDirArgsForCall []struct {
	}
	getCgroupParentDirReturns struct {
		result1 string
	}
	getCgroupParentDirReturnsOnCall map[int]struct {
		result1 string
	}
	GetContainerStub        func(string) (cache.Container, bool)
	getContainerMutex       sync.RWMutex
	getContainerArgsForCall []struct {
		arg1 string
	}
	getContainerReturns struct {
		result1 cache.Container
		result2 bool
	}
	getContainerReturnsOnCall map[int]struct {
		result1 cache.Container
		result2 bool
	}
	GetContainersStub        func() []cache.Container
	getContainersMutex       sync.RWMutex
	getContainersArgsForCall []struct {
	}
	getContainersReturns struct {
		result1 []cache.Container
	}
	getContainersReturnsOnCall map[int]struct {
		result1 []cache.Container
	}
	GetIDStub        func() string
	getIDMutex       sync.RWMutex
	getIDArgsForCall []struct {
	}
	getIDReturns struct {
		result1 string
	}
	getIDReturnsOnCall map[int]struct {
		result1 string
	}
	GetLabelsStub        func() map[string]string
	getLabelsMutex       sync.RWMutex
	getLabelsArgsForCall []struct {
	}
	getLabelsReturns struct {
		result1 map[string]string
	}
	getLabelsReturnsOnCall map[int]struct {
		result1 map[string]string
	}
	GetLinuxResourcesStub        func() *v1alpha1.LinuxContainerResources
	getLinuxResourcesMutex       sync.RWMutex
	getLinuxResourcesArgsForCall []struct {
	}
	getLinuxResourcesReturns struct {
		result1 *v1alpha1.LinuxContainerResources
	}
	getLinuxResourcesReturnsOnCall map[int]struct {
		result1 *v1alpha1.LinuxContainerResources
	}
	GetNameStub        func() string
	getNameMutex       sync.RWMutex
	getNameArgsForCall []struct {
	}
	getNameReturns struct {
		result1 string
	}
	getNameReturnsOnCall map[int]struct {
		result1 string
	}
	GetNamespaceStub        func() string
	getNamespaceMutex       sync.RWMutex
	getNamespaceArgsForCall []struct {
	}
	getNamespaceReturns struct {
		result1 string
	}
	getNamespaceReturnsOnCall map[int]struct {
		result1 string
	}
	GetPodResourceRequirementsStub        func() v1.ResourceRequirements
	getPodResourceRequirementsMutex       sync.RWMutex
	getPodResourceRequirementsArgsForCall []struct {
	}
	getPodResourceRequirementsReturns struct {
		result1 v1.ResourceRequirements
	}
	getPodResourceRequirementsReturnsOnCall map[int]struct {
		result1 v1.ResourceRequirements
	}
	GetQOSClassStub        func() v1.PodQOSClass
	getQOSClassMutex       sync.RWMutex
	getQOSClassArgsForCall []struct {
	}
	getQOSClassReturns struct {
		result1 v1.PodQOSClass
	}
	getQOSClassReturnsOnCall map[int]struct {
		result1 v1.PodQOSClass
	}
	GetStateStub        func() cache.PodState
	getStateMutex       sync.RWMutex
	getStateArgsForCall []struct {
	}
	getStateReturns struct {
		result1 cache.PodState
	}
	getStateReturnsOnCall map[int]struct {
		result1 cache.PodState
	}
	GetUIDStub        func() string
	getUIDMutex       sync.RWMutex
	getUIDArgsForCall []struct {
	}
	getUIDReturns struct {
		result1 string
	}
	getUIDReturnsOnCall map[int]struct {
		result1 string
	}
	StringStub        func() string
	stringMutex       sync.RWMutex
	stringArgsForCall []struct {
	}
	stringReturns struct {
		result1 string
	}
	stringReturnsOnCall map[int]struct {
		result1 string
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakePod) GetAnnotations() map[string]string {
	fake.getAnnotationsMutex.Lock()
	ret, specificReturn := fake.getAnnotationsReturnsOnCall[len(fake.getAnnotationsArgsForCall)]
	fake.getAnnotationsArgsForCall = append(fake.getAnnotationsArgsForCall, struct {
	}{})
	stub := fake.GetAnnotationsStub
	fakeReturns := fake.getAnnotationsReturns
	fake.recordInvocation("GetAnnotations", []interface{}{})
	fake.getAnnotationsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetAnnotationsCallCount() int {
	fake.getAnnotationsMutex.RLock()
	defer fake.getAnnotationsMutex.RUnlock()
	return len(fake.getAnnotationsArgsForCall)
}

func (fake *FakePod) GetAnnotationsCalls(stub func() map[string]string) {
	fake.getAnnotationsMutex.Lock()
	defer fake.getAnnotationsMutex.Unlock()
	fake.GetAnnotationsStub = stub
}

func (fake *FakePod) GetAnnotationsReturns(result1 map[string]string) {
	fake.getAnnotationsMutex.Lock()
	defer fake.getAnnotationsMutex.Unlock()
	fake.GetAnnotationsStub = nil
	fake.getAnnotationsReturns = struct {
		result1 map[string]string
	}{result1}
}

func (fake *FakePod) GetAnnotationsReturnsOnCall(i int, result1 map[string]string) {
	fake.getAnnotationsMutex.Lock()
	defer fake.getAnnotationsMutex.Unlock()
	fake.GetAnnotationsStub = nil
	if fake.getAnnotationsReturnsOnCall == nil {
		fake.getAnnotationsReturnsOnCall = make(map[int]struct {
			result1 map[string]string
		})
	}
	fake.getAnnotationsReturnsOnCall[i] = struct {
		result1 map[string]string
	}{result1}
}

func (fake *FakePod) GetCgroupParentDir() string {
	fake.getCgroupParentDirMutex.Lock()
	ret, specificReturn := fake.getCgroupParentDirReturnsOnCall[len(fake.getCgroupParentDirArgsForCall)]
	fake.getCgroupParentDirArgsForCall = append(fake.getCgroupParentDirArgsForCall, struct {
	}{})
	stub := fake.GetCgroupParentDirStub
	fakeReturns := fake.getCgroupParentDirReturns
	fake.recordInvocation("GetCgroupParentDir", []interface{}{})
	fake.getCgroupParentDirMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetCgroupParentDirCallCount() int {
	fake.getCgroupParentDirMutex.RLock()
	defer fake.getCgroupParentDirMutex.RUnlock()
	return len(fake.getCgroupParentDirArgsForCall)
}

func (fake *FakePod) GetCgroupParentDirCalls(stub func() string) {
	fake.getCgroupParentDirMutex.Lock()
	defer fake.getCgroupParentDirMutex.Unlock()
	fake.GetCgroupParentDirStub = stub
}

func (fake *FakePod) GetCgroupParentDirReturns(result1 string) {
	fake.getCgroupParentDirMutex.Lock()
	defer fake.getCgroupParentDirMutex.Unlock()
	fake.GetCgroupParentDirStub = nil
	fake.getCgroupParentDirReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetCgroupParentDirReturnsOnCall(i int, result1 string) {
	fake.getCgroupParentDirMutex.Lock()
	defer fake.getCgroupParentDirMutex.Unlock()
	fake.GetCgroupParentDirStub = nil
	if fake.getCgroupParentDirReturnsOnCall == nil {
		fake.getCgroupParentDirReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.getCgroupParentDirReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetContainer(arg1 string) (cache.Container, bool) {
	fake.getContainerMutex.Lock()
	ret, specificReturn := fake.getContainerReturnsOnCall[len(fake.getContainerArgsForCall)]
	fake.getContainerArgsForCall = append(fake.getContainerArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.GetContainerStub
	fakeReturns := fake.getContainerReturns
	fake.recordInvocation("GetContainer", []interface{}{arg1})
	fake.getContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakePod) GetContainerCallCount() int {
	fake.getContainerMutex.RLock()
	defer fake.getContainerMutex.RUnlock()
	return len(fake.getContainerArgsForCall)
}

func (fake *FakePod) GetContainerCalls(stub func(string) (cache.Container, bool)) {
	fake.getContainerMutex.Lock()
	defer fake.getContainerMutex.Unlock()
	fake.GetContainerStub = stub
}

func (fake *FakePod) GetContainerArgsForCall(i int) string {
	fake.getContainerMutex.RLock()
	defer fake.getContainerMutex.RUnlock()
	argsForCall := fake.getContainerArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakePod) GetContainerReturns(result1 cache.Container, result2 bool) {
	fake.getContainerMutex.Lock()
	defer fake.getContainerMutex.Unlock()
	fake.GetContainerStub = nil
	fake.getContainerReturns = struct {
		result1 cache.Container
		result2 bool
	}{result1, result2}
}

func (fake *FakePod) GetContainerReturnsOnCall(i int, result1 cache.Container, result2 bool) {
	fake.getContainerMutex.Lock()
	defer fake.getContainerMutex.Unlock()
	fake.GetContainerStub = nil
	if fake.getContainerReturnsOnCall == nil {
		fake.getContainerReturnsOnCall = make(map[int]struct {
			result1 cache.Container
			result2 bool
		})
	}
	fake.getContainerReturnsOnCall[i] = struct {
		result1 cache.Container
		result2 bool
	}{result1, result2}
}

func (fake *FakePod) GetContainers() []cache.Container {
	fake.getContainersMutex.Lock()
	ret, specificReturn := fake.getContainersReturnsOnCall[len(fake.getContainersArgsForCall)]
	fake.getContainersArgsForCall = append(fake.getContainersArgsForCall, struct {
	}{})
	stub := fake.GetContainersStub
	fakeReturns := fake.getContainersReturns
	fake.recordInvocation("GetContainers", []interface{}{})
	fake.getContainersMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetContainersCallCount() int {
	fake.getContainersMutex.RLock()
	defer fake.getContainersMutex.RUnlock()
	return len(fake.getContainersArgsForCall)
}

func (fake *FakePod) GetContainersCalls(stub func() []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = stub
}

func (fake *FakePod) GetContainersReturns(result1 []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = nil
	fake.getContainersReturns = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakePod) GetContainersReturnsOnCall(i int, result1 []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = nil
	if fake.getContainersReturnsOnCall == nil {
		fake.getContainersReturnsOnCall = make(map[int]struct {
			result1 []cache.Container
		})
	}
	fake.getContainersReturnsOnCall[i] = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakePod) GetID() string {
	fake.getIDMutex.Lock()
	ret, specificReturn := fake.getIDReturnsOnCall[len(fake.getIDArgsForCall)]
	fake.getIDArgsForCall = append(fake.getIDArgsForCall, struct {
	}{})
	stub := fake.GetIDStub
	fakeReturns := fake.getIDReturns
	fake.recordInvocation("GetID", []interface{}{})
	fake.getIDMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetIDCallCount() int {
	fake.getIDMutex.RLock()
	defer fake.getIDMutex.RUnlock()
	return len(fake.getIDArgsForCall)
}

func (fake *FakePod) GetIDCalls(stub func() string) {
	fake.getIDMutex.Lock()
	defer fake.getIDMutex.Unlock()
	fake.GetIDStub = stub
}

func (fake *FakePod) GetIDReturns(result1 string) {
	fake.getIDMutex.Lock()
	defer fake.getIDMutex.Unlock()
	fake.GetIDStub = nil
	fake.getIDReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetIDReturnsOnCall(i int, result1 string) {
	fake.getIDMutex.Lock()
	defer fake.getIDMutex.Unlock()
	fake.GetIDStub = nil
	if fake.getIDReturnsOnCall == nil {
		fake.getIDReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.getIDReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetLabels() map[string]string {
	fake.getLabelsMutex.Lock()
	ret, specificReturn := fake.getLabelsReturnsOnCall[len(fake.getLabelsArgsForCall)]
	fake.getLabelsArgsForCall = append(fake.getLabelsArgsForCall, struct {
	}{})
	stub := fake.GetLabelsStub
	fakeReturns := fake.getLabelsReturns
	fake.recordInvocation("GetLabels", []interface{}{})
	fake.getLabelsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetLabelsCallCount() int {
	fake.getLabelsMutex.RLock()
	defer fake.getLabelsMutex.RUnlock()
	return len(fake.getLabelsArgsForCall)
}

func (fake *FakePod) GetLabelsCalls(stub func() map[string]string) {
	fake.getLabelsMutex.Lock()
	defer fake.getLabelsMutex.Unlock()
	fake.GetLabelsStub = stub
}

func (fake *FakePod) GetLabelsReturns(result1 map[string]string) {
	fake.getLabelsMutex.Lock()
	defer fake.getLabelsMutex.Unlock()
	fake.GetLabelsStub = nil
	fake.getLabelsReturns = struct {
		result1 map[string]string
	}{result1}
}

func (fake *FakePod) GetLabelsReturnsOnCall(i int, result1 map[string]string) {
	fake.getLabelsMutex.Lock()
	defer fake.getLabelsMutex.Unlock()
	fake.GetLabelsStub = nil
	if fake.getLabelsReturnsOnCall == nil {
		fake.getLabelsReturnsOnCall = make(map[int]struct {
			result1 map[string]string
		})
	}
	fake.getLabelsReturnsOnCall[i] = struct {
		result1 map[string]string
	}{result1}
}

func (fake *FakePod) GetLinuxResources() *v1alpha1.LinuxContainerResources {
	fake.getLinuxResourcesMutex.Lock()
	ret, specificReturn := fake.getLinuxResourcesReturnsOnCall[len(fake.getLinuxResourcesArgsForCall)]
	fake.getLinuxResourcesArgsForCall = append(fake.getLinuxResourcesArgsForCall, struct {
	}{})
	stub := fake.GetLinuxResourcesStub
	fakeReturns := fake.getLinuxResourcesReturns
	fake.recordInvocation("GetLinuxResources", []interface{}{})
	fake.getLinuxResourcesMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetLinuxResourcesCallCount() int {
	fake.getLinuxResourcesMutex.RLock()
	defer fake.getLinuxResourcesMutex.RUnlock()
	return len(fake.getLinuxResourcesArgsForCall)
}

func (fake *FakePod) GetLinuxResourcesCalls(stub func() *v1alpha1.LinuxContainerResources) {
	fake.getLinuxResourcesMutex.Lock()
	defer fake.getLinuxResourcesMutex.Unlock()
	fake.GetLinuxResourcesStub = stub
}

func (fake *FakePod) GetLinuxResourcesReturns(result1 *v1alpha1.LinuxContainerResources) {
	fake.getLinuxResourcesMutex.Lock()
	defer fake.getLinuxResourcesMutex.Unlock()
	fake.GetLinuxResourcesStub = nil
	fake.getLinuxResourcesReturns = struct {
		result1 *v1alpha1.LinuxContainerResources
	}{result1}
}

func (fake *FakePod) GetLinuxResourcesReturnsOnCall(i int, result1 *v1alpha1.LinuxContainerResources) {
	fake.getLinuxResourcesMutex.Lock()
	defer fake.getLinuxResourcesMutex.Unlock()
	fake.GetLinuxResourcesStub = nil
	if fake.getLinuxResourcesReturnsOnCall == nil {
		fake.getLinuxResourcesReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.LinuxContainerResources
		})
	}
	fake.getLinuxResourcesReturnsOnCall[i] = struct {
		result1 *v1alpha1.LinuxContainerResources
	}{result1}
}

func (fake *FakePod) GetName() string {
	fake.getNameMutex.Lock()
	ret, specificReturn := fake.getNameReturnsOnCall[len(fake.getNameArgsForCall)]
	fake.getNameArgsForCall = append(fake.getNameArgsForCall, struct {
	}{})
	stub := fake.GetNameStub
	fakeReturns := fake.getNameReturns
	fake.recordInvocation("GetName", []interface{}{})
	fake.getNameMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetNameCallCount() int {
	fake.getNameMutex.RLock()
	defer fake.getNameMutex.RUnlock()
	return len(fake.getNameArgsForCall)
}

func (fake *FakePod) GetNameCalls(stub func() string) {
	fake.getNameMutex.Lock()
	defer fake.getNameMutex.Unlock()
	fake.GetNameStub = stub
}

func (fake *FakePod) GetNameReturns(result1 string) {
	fake.getNameMutex.Lock()
	defer fake.getNameMutex.Unlock()
	fake.GetNameStub = nil
	fake.getNameReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetNameReturnsOnCall(i int, result1 string) {
	fake.getNameMutex.Lock()
	defer fake.getNameMutex.Unlock()
	fake.GetNameStub = nil
	if fake.getNameReturnsOnCall == nil {
		fake.getNameReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.getNameReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetNamespace() string {
	fake.getNamespaceMutex.Lock()
	ret, specificReturn := fake.getNamespaceReturnsOnCall[len(fake.getNamespaceArgsForCall)]
	fake.getNamespaceArgsForCall = append(fake.getNamespaceArgsForCall, struct {
	}{})
	stub := fake.GetNamespaceStub
	fakeReturns := fake.getNamespaceReturns
	fake.recordInvocation("GetNamespace", []interface{}{})
	fake.getNamespaceMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetNamespaceCallCount() int {
	fake.getNamespaceMutex.RLock()
	defer fake.getNamespaceMutex.RUnlock()
	return len(fake.getNamespaceArgsForCall)
}

func (fake *FakePod) GetNamespaceCalls(stub func() string) {
	fake.getNamespaceMutex.Lock()
	defer fake.getNamespaceMutex.Unlock()
	fake.GetNamespaceStub = stub
}

func (fake *FakePod) GetNamespaceReturns(result1 string) {
	fake.getNamespaceMutex.Lock()
	defer fake.getNamespaceMutex.Unlock()
	fake.GetNamespaceStub = nil
	fake.getNamespaceReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetNamespaceReturnsOnCall(i int, result1 string) {
	fake.getNamespaceMutex.Lock()
	defer fake.getNamespaceMutex.Unlock()
	fake.GetNamespaceStub = nil
	if fake.getNamespaceReturnsOnCall == nil {
		fake.getNamespaceReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.getNamespaceReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetPodResourceRequirements() v1.ResourceRequirements {
	fake.getPodResourceRequirementsMutex.Lock()
	ret, specificReturn := fake.getPodResourceRequirementsReturnsOnCall[len(fake.getPodResourceRequirementsArgsForCall)]
	fake.getPodResourceRequirementsArgsForCall = append(fake.getPodResourceRequirementsArgsForCall, struct {
	}{})
	stub := fake.GetPodResourceRequirementsStub
	fakeReturns := fake.getPodResourceRequirementsReturns
	fake.recordInvocation("GetPodResourceRequirements", []interface{}{})
	fake.getPodResourceRequirementsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetPodResourceRequirementsCallCount() int {
	fake.getPodResourceRequirementsMutex.RLock()
	defer fake.getPodResourceRequirementsMutex.RUnlock()
	return len(fake.getPodResourceRequirementsArgsForCall)
}

func (fake *FakePod) GetPodResourceRequirementsCalls(stub func() v1.ResourceRequirements) {
	fake.getPodResourceRequirementsMutex.Lock()
	defer fake.getPodResourceRequirementsMutex.Unlock()
	fake.GetPodResourceRequirementsStub = stub
}

func (fake *FakePod) GetPodResourceRequirementsReturns(result1 v1.ResourceRequirements) {
	fake.getPodResourceRequirementsMutex.Lock()
	defer fake.getPodResourceRequirementsMutex.Unlock()
	fake.GetPodResourceRequirementsStub = nil
	fake.getPodResourceRequirementsReturns = struct {
		result1 v1.ResourceRequirements
	}{result1}
}

func (fake *FakePod) GetPodResourceRequirementsReturnsOnCall(i int, result1 v1.ResourceRequirements) {
	fake.getPodResourceRequirementsMutex.Lock()
	defer fake.getPodResourceRequirementsMutex.Unlock()
	fake.GetPodResourceRequirementsStub = nil
	if fake.getPodResourceRequirementsReturnsOnCall == nil {
		fake.getPodResourceRequirementsReturnsOnCall = make(map[int]struct {
			result1 v1.ResourceRequirements
		})
	}
	fake.getPodResourceRequirementsReturnsOnCall[i] = struct {
		result1 v1.ResourceRequirements
	}{result1}
}

func (fake *FakePod) GetQOSClass() v1.PodQOSClass {
	fake.getQOSClassMutex.Lock()
	ret, specificReturn := fake.getQOSClassReturnsOnCall[len(fake.getQOSClassArgsForCall)]
	fake.getQOSClassArgsForCall = append(fake.getQOSClassArgsForCall, struct {
	}{})
	stub := fake.GetQOSClassStub
	fakeReturns := fake.getQOSClassReturns
	fake.recordInvocation("GetQOSClass", []interface{}{})
	fake.getQOSClassMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetQOSClassCallCount() int {
	fake.getQOSClassMutex.RLock()
	defer fake.getQOSClassMutex.RUnlock()
	return len(fake.getQOSClassArgsForCall)
}

func (fake *FakePod) GetQOSClassCalls(stub func() v1.PodQOSClass) {
	fake.getQOSClassMutex.Lock()
	defer fake.getQOSClassMutex.Unlock()
	fake.GetQOSClassStub = stub
}

func (fake *FakePod) GetQOSClassReturns(result1 v1.PodQOSClass) {
	fake.getQOSClassMutex.Lock()
	defer fake.getQOSClassMutex.Unlock()
	fake.GetQOSClassStub = nil
	fake.getQOSClassReturns = struct {
		result1 v1.PodQOSClass
	}{result1}
}

func (fake *FakePod) GetQOSClassReturnsOnCall(i int, result1 v1.PodQOSClass) {
	fake.getQOSClassMutex.Lock()
	defer fake.getQOSClassMutex.Unlock()
	fake.GetQOSClassStub = nil
	if fake.getQOSClassReturnsOnCall == nil {
		fake.getQOSClassReturnsOnCall = make(map[int]struct {
			result1 v1.PodQOSClass
		})
	}
	fake.getQOSClassReturnsOnCall[i] = struct {
		result1 v1.PodQOSClass
	}{result1}
}

func (fake *FakePod) GetState() cache.PodState {
	fake.getStateMutex.Lock()
	ret, specificReturn := fake.getStateReturnsOnCall[len(fake.getStateArgsForCall)]
	fake.getStateArgsForCall = append(fake.getStateArgsForCall, struct {
	}{})
	stub := fake.GetStateStub
	fakeReturns := fake.getStateReturns
	fake.recordInvocation("GetState", []interface{}{})
	fake.getStateMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetStateCallCount() int {
	fake.getStateMutex.RLock()
	defer fake.getStateMutex.RUnlock()
	return len(fake.getStateArgsForCall)
}

func (fake *FakePod) GetStateCalls(stub func() cache.PodState) {
	fake.getStateMutex.Lock()
	defer fake.getStateMutex.Unlock()
	fake.GetStateStub = stub
}

func (fake *FakePod) GetStateReturns(result1 cache.PodState) {
	fake.getStateMutex.Lock()
	defer fake.getStateMutex.Unlock()
	fake.GetStateStub = nil
	fake.getStateReturns = struct {
		result1 cache.PodState
	}{result1}
}

func (fake *FakePod) GetStateReturnsOnCall(i int, result1 cache.PodState) {
	fake.getStateMutex.Lock()
	defer fake.getStateMutex.Unlock()
	fake.GetStateStub = nil
	if fake.getStateReturnsOnCall == nil {
		fake.getStateReturnsOnCall = make(map[int]struct {
			result1 cache.PodState
		})
	}
	fake.getStateReturnsOnCall[i] = struct {
		result1 cache.PodState
	}{result1}
}

func (fake *FakePod) GetUID() string {
	fake.getUIDMutex.Lock()
	ret, specificReturn := fake.getUIDReturnsOnCall[len(fake.getUIDArgsForCall)]
	fake.getUIDArgsForCall = append(fake.getUIDArgsForCall, struct {
	}{})
	stub := fake.GetUIDStub
	fakeReturns := fake.getUIDReturns
	fake.recordInvocation("GetUID", []interface{}{})
	fake.getUIDMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) GetUIDCallCount() int {
	fake.getUIDMutex.RLock()
	defer fake.getUIDMutex.RUnlock()
	return len(fake.getUIDArgsForCall)
}

func (fake *FakePod) GetUIDCalls(stub func() string) {
	fake.getUIDMutex.Lock()
	defer fake.getUIDMutex.Unlock()
	fake.GetUIDStub = stub
}

func (fake *FakePod) GetUIDReturns(result1 string) {
	fake.getUIDMutex.Lock()
	defer fake.getUIDMutex.Unlock()
	fake.GetUIDStub = nil
	fake.getUIDReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) GetUIDReturnsOnCall(i int, result1 string) {
	fake.getUIDMutex.Lock()
	defer fake.getUIDMutex.Unlock()
	fake.GetUIDStub = nil
	if fake.getUIDReturnsOnCall == nil {
		fake.getUIDReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.getUIDReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) String() string {
	fake.stringMutex.Lock()
	ret, specificReturn := fake.stringReturnsOnCall[len(fake.stringArgsForCall)]
	fake.stringArgsForCall = append(fake.stringArgsForCall, struct {
	}{})
	stub := fake.StringStub
	fakeReturns := fake.stringReturns
	fake.recordInvocation("String", []interface{}{})
	fake.stringMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakePod) StringCallCount() int {
	fake.stringMutex.RLock()
	defer fake.stringMutex.RUnlock()
	return len(fake.stringArgsForCall)
}

func (fake *FakePod) StringCalls(stub func() string) {
	fake.stringMutex.Lock()
	defer fake.stringMutex.Unlock()
	fake.StringStub = stub
}

func (fake *FakePod) StringReturns(result1 string) {
	fake.stringMutex.Lock()
	defer fake.stringMutex.Unlock()
	fake.StringStub = nil
	fake.stringReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) StringReturnsOnCall(i int, result1 string) {
	fake.stringMutex.Lock()
	defer fake.stringMutex.Unlock()
	fake.StringStub = nil
	if fake.stringReturnsOnCall == nil {
		fake.stringReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.stringReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakePod) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.getAnnotationsMutex.RLock()
	defer fake.getAnnotationsMutex.RUnlock()
	fake.getCgroupParentDirMutex.RLock()
	defer fake.getCgroupParentDirMutex.RUnlock()
	fake.getContainerMutex.RLock()
	defer fake.getContainerMutex.RUnlock()
	fake.getContainersMutex.RLock()
	defer fake.getContainersMutex.RUnlock()
	fake.getIDMutex.RLock()
	defer fake.getIDMutex.RUnlock()
	fake.getLabelsMutex.RLock()
	defer fake.getLabelsMutex.RUnlock()
	fake.getLinuxResourcesMutex.RLock()
	defer fake.getLinuxResourcesMutex.RUnlock()
	fake.getNameMutex.RLock()
	defer fake.getNameMutex.RUnlock()
	fake.getNamespaceMutex.RLock()
	defer fake.getNamespaceMutex.RUnlock()
	fake.getPodResourceRequirementsMutex.RLock()
	defer fake.getPodResourceRequirementsMutex.RUnlock()
	fake.getQOSClassMutex.RLock()
	defer fake.getQOSClassMutex.RUnlock()
	fake.getStateMutex.RLock()
	defer fake.getStateMutex.RUnlock()
	fake.getUIDMutex.RLock()
	defer fake.getUIDMutex.RUnlock()
	fake.stringMutex.RLock()
	defer fake.stringMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakePod) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ cache.Pod = new(FakePod)
