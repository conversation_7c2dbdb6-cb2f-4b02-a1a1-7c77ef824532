// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"context"
	"sync"

	"google.golang.org/grpc"
	"kunpeng.huawei.com/tap/pkg/policy"
	"kunpeng.huawei.com/tap/pkg/server/dispatcher"
)

type FakeDispatcher struct {
	BackfillRequestStub        func(interface{}, interface{}, interface{})
	backfillRequestMutex       sync.RWMutex
	backfillRequestArgsForCall []struct {
		arg1 interface{}
		arg2 interface{}
		arg3 interface{}
	}
	DeleteFromCacheIfNeedStub        func(interface{})
	deleteFromCacheIfNeedMutex       sync.RWMutex
	deleteFromCacheIfNeedArgsForCall []struct {
		arg1 interface{}
	}
	DispatchStub        func(context.Context, policy.HookType, interface{}, map[string]string) interface{}
	dispatchMutex       sync.RWMutex
	dispatchArgsForCall []struct {
		arg1 context.Context
		arg2 policy.HookType
		arg3 interface{}
		arg4 map[string]string
	}
	dispatchReturns struct {
		result1 interface{}
	}
	dispatchReturnsOnCall map[int]struct {
		result1 interface{}
	}
	InsertIntoCacheIfNeedStub        func(interface{}, interface{})
	insertIntoCacheIfNeedMutex       sync.RWMutex
	insertIntoCacheIfNeedArgsForCall []struct {
		arg1 interface{}
		arg2 interface{}
	}
	InterceptRuntimeRequestStub        func(context.Context, policy.HookType, interface{}, map[string]string, grpc.UnaryHandler) (interface{}, error)
	interceptRuntimeRequestMutex       sync.RWMutex
	interceptRuntimeRequestArgsForCall []struct {
		arg1 context.Context
		arg2 policy.HookType
		arg3 interface{}
		arg4 map[string]string
		arg5 grpc.UnaryHandler
	}
	interceptRuntimeRequestReturns struct {
		result1 interface{}
		result2 error
	}
	interceptRuntimeRequestReturnsOnCall map[int]struct {
		result1 interface{}
		result2 error
	}
	ParseContainerRequestStub        func(interface{}) interface{}
	parseContainerRequestMutex       sync.RWMutex
	parseContainerRequestArgsForCall []struct {
		arg1 interface{}
	}
	parseContainerRequestReturns struct {
		result1 interface{}
	}
	parseContainerRequestReturnsOnCall map[int]struct {
		result1 interface{}
	}
	ParsePodRequestStub        func(interface{}) interface{}
	parsePodRequestMutex       sync.RWMutex
	parsePodRequestArgsForCall []struct {
		arg1 interface{}
	}
	parsePodRequestReturns struct {
		result1 interface{}
	}
	parsePodRequestReturnsOnCall map[int]struct {
		result1 interface{}
	}
	SetDockerCgroupDriverStub        func(string)
	setDockerCgroupDriverMutex       sync.RWMutex
	setDockerCgroupDriverArgsForCall []struct {
		arg1 string
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeDispatcher) BackfillRequest(arg1 interface{}, arg2 interface{}, arg3 interface{}) {
	fake.backfillRequestMutex.Lock()
	fake.backfillRequestArgsForCall = append(fake.backfillRequestArgsForCall, struct {
		arg1 interface{}
		arg2 interface{}
		arg3 interface{}
	}{arg1, arg2, arg3})
	stub := fake.BackfillRequestStub
	fake.recordInvocation("BackfillRequest", []interface{}{arg1, arg2, arg3})
	fake.backfillRequestMutex.Unlock()
	if stub != nil {
		fake.BackfillRequestStub(arg1, arg2, arg3)
	}
}

func (fake *FakeDispatcher) BackfillRequestCallCount() int {
	fake.backfillRequestMutex.RLock()
	defer fake.backfillRequestMutex.RUnlock()
	return len(fake.backfillRequestArgsForCall)
}

func (fake *FakeDispatcher) BackfillRequestCalls(stub func(interface{}, interface{}, interface{})) {
	fake.backfillRequestMutex.Lock()
	defer fake.backfillRequestMutex.Unlock()
	fake.BackfillRequestStub = stub
}

func (fake *FakeDispatcher) BackfillRequestArgsForCall(i int) (interface{}, interface{}, interface{}) {
	fake.backfillRequestMutex.RLock()
	defer fake.backfillRequestMutex.RUnlock()
	argsForCall := fake.backfillRequestArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeDispatcher) DeleteFromCacheIfNeed(arg1 interface{}) {
	fake.deleteFromCacheIfNeedMutex.Lock()
	fake.deleteFromCacheIfNeedArgsForCall = append(fake.deleteFromCacheIfNeedArgsForCall, struct {
		arg1 interface{}
	}{arg1})
	stub := fake.DeleteFromCacheIfNeedStub
	fake.recordInvocation("DeleteFromCacheIfNeed", []interface{}{arg1})
	fake.deleteFromCacheIfNeedMutex.Unlock()
	if stub != nil {
		fake.DeleteFromCacheIfNeedStub(arg1)
	}
}

func (fake *FakeDispatcher) DeleteFromCacheIfNeedCallCount() int {
	fake.deleteFromCacheIfNeedMutex.RLock()
	defer fake.deleteFromCacheIfNeedMutex.RUnlock()
	return len(fake.deleteFromCacheIfNeedArgsForCall)
}

func (fake *FakeDispatcher) DeleteFromCacheIfNeedCalls(stub func(interface{})) {
	fake.deleteFromCacheIfNeedMutex.Lock()
	defer fake.deleteFromCacheIfNeedMutex.Unlock()
	fake.DeleteFromCacheIfNeedStub = stub
}

func (fake *FakeDispatcher) DeleteFromCacheIfNeedArgsForCall(i int) interface{} {
	fake.deleteFromCacheIfNeedMutex.RLock()
	defer fake.deleteFromCacheIfNeedMutex.RUnlock()
	argsForCall := fake.deleteFromCacheIfNeedArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeDispatcher) Dispatch(arg1 context.Context, arg2 policy.HookType, arg3 interface{}, arg4 map[string]string) interface{} {
	fake.dispatchMutex.Lock()
	ret, specificReturn := fake.dispatchReturnsOnCall[len(fake.dispatchArgsForCall)]
	fake.dispatchArgsForCall = append(fake.dispatchArgsForCall, struct {
		arg1 context.Context
		arg2 policy.HookType
		arg3 interface{}
		arg4 map[string]string
	}{arg1, arg2, arg3, arg4})
	stub := fake.DispatchStub
	fakeReturns := fake.dispatchReturns
	fake.recordInvocation("Dispatch", []interface{}{arg1, arg2, arg3, arg4})
	fake.dispatchMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDispatcher) DispatchCallCount() int {
	fake.dispatchMutex.RLock()
	defer fake.dispatchMutex.RUnlock()
	return len(fake.dispatchArgsForCall)
}

func (fake *FakeDispatcher) DispatchCalls(stub func(context.Context, policy.HookType, interface{}, map[string]string) interface{}) {
	fake.dispatchMutex.Lock()
	defer fake.dispatchMutex.Unlock()
	fake.DispatchStub = stub
}

func (fake *FakeDispatcher) DispatchArgsForCall(i int) (context.Context, policy.HookType, interface{}, map[string]string) {
	fake.dispatchMutex.RLock()
	defer fake.dispatchMutex.RUnlock()
	argsForCall := fake.dispatchArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeDispatcher) DispatchReturns(result1 interface{}) {
	fake.dispatchMutex.Lock()
	defer fake.dispatchMutex.Unlock()
	fake.DispatchStub = nil
	fake.dispatchReturns = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) DispatchReturnsOnCall(i int, result1 interface{}) {
	fake.dispatchMutex.Lock()
	defer fake.dispatchMutex.Unlock()
	fake.DispatchStub = nil
	if fake.dispatchReturnsOnCall == nil {
		fake.dispatchReturnsOnCall = make(map[int]struct {
			result1 interface{}
		})
	}
	fake.dispatchReturnsOnCall[i] = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) InsertIntoCacheIfNeed(arg1 interface{}, arg2 interface{}) {
	fake.insertIntoCacheIfNeedMutex.Lock()
	fake.insertIntoCacheIfNeedArgsForCall = append(fake.insertIntoCacheIfNeedArgsForCall, struct {
		arg1 interface{}
		arg2 interface{}
	}{arg1, arg2})
	stub := fake.InsertIntoCacheIfNeedStub
	fake.recordInvocation("InsertIntoCacheIfNeed", []interface{}{arg1, arg2})
	fake.insertIntoCacheIfNeedMutex.Unlock()
	if stub != nil {
		fake.InsertIntoCacheIfNeedStub(arg1, arg2)
	}
}

func (fake *FakeDispatcher) InsertIntoCacheIfNeedCallCount() int {
	fake.insertIntoCacheIfNeedMutex.RLock()
	defer fake.insertIntoCacheIfNeedMutex.RUnlock()
	return len(fake.insertIntoCacheIfNeedArgsForCall)
}

func (fake *FakeDispatcher) InsertIntoCacheIfNeedCalls(stub func(interface{}, interface{})) {
	fake.insertIntoCacheIfNeedMutex.Lock()
	defer fake.insertIntoCacheIfNeedMutex.Unlock()
	fake.InsertIntoCacheIfNeedStub = stub
}

func (fake *FakeDispatcher) InsertIntoCacheIfNeedArgsForCall(i int) (interface{}, interface{}) {
	fake.insertIntoCacheIfNeedMutex.RLock()
	defer fake.insertIntoCacheIfNeedMutex.RUnlock()
	argsForCall := fake.insertIntoCacheIfNeedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeDispatcher) InterceptRuntimeRequest(arg1 context.Context, arg2 policy.HookType, arg3 interface{}, arg4 map[string]string, arg5 grpc.UnaryHandler) (interface{}, error) {
	fake.interceptRuntimeRequestMutex.Lock()
	ret, specificReturn := fake.interceptRuntimeRequestReturnsOnCall[len(fake.interceptRuntimeRequestArgsForCall)]
	fake.interceptRuntimeRequestArgsForCall = append(fake.interceptRuntimeRequestArgsForCall, struct {
		arg1 context.Context
		arg2 policy.HookType
		arg3 interface{}
		arg4 map[string]string
		arg5 grpc.UnaryHandler
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.InterceptRuntimeRequestStub
	fakeReturns := fake.interceptRuntimeRequestReturns
	fake.recordInvocation("InterceptRuntimeRequest", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.interceptRuntimeRequestMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4, arg5)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeDispatcher) InterceptRuntimeRequestCallCount() int {
	fake.interceptRuntimeRequestMutex.RLock()
	defer fake.interceptRuntimeRequestMutex.RUnlock()
	return len(fake.interceptRuntimeRequestArgsForCall)
}

func (fake *FakeDispatcher) InterceptRuntimeRequestCalls(stub func(context.Context, policy.HookType, interface{}, map[string]string, grpc.UnaryHandler) (interface{}, error)) {
	fake.interceptRuntimeRequestMutex.Lock()
	defer fake.interceptRuntimeRequestMutex.Unlock()
	fake.InterceptRuntimeRequestStub = stub
}

func (fake *FakeDispatcher) InterceptRuntimeRequestArgsForCall(i int) (context.Context, policy.HookType, interface{}, map[string]string, grpc.UnaryHandler) {
	fake.interceptRuntimeRequestMutex.RLock()
	defer fake.interceptRuntimeRequestMutex.RUnlock()
	argsForCall := fake.interceptRuntimeRequestArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeDispatcher) InterceptRuntimeRequestReturns(result1 interface{}, result2 error) {
	fake.interceptRuntimeRequestMutex.Lock()
	defer fake.interceptRuntimeRequestMutex.Unlock()
	fake.InterceptRuntimeRequestStub = nil
	fake.interceptRuntimeRequestReturns = struct {
		result1 interface{}
		result2 error
	}{result1, result2}
}

func (fake *FakeDispatcher) InterceptRuntimeRequestReturnsOnCall(i int, result1 interface{}, result2 error) {
	fake.interceptRuntimeRequestMutex.Lock()
	defer fake.interceptRuntimeRequestMutex.Unlock()
	fake.InterceptRuntimeRequestStub = nil
	if fake.interceptRuntimeRequestReturnsOnCall == nil {
		fake.interceptRuntimeRequestReturnsOnCall = make(map[int]struct {
			result1 interface{}
			result2 error
		})
	}
	fake.interceptRuntimeRequestReturnsOnCall[i] = struct {
		result1 interface{}
		result2 error
	}{result1, result2}
}

func (fake *FakeDispatcher) ParseContainerRequest(arg1 interface{}) interface{} {
	fake.parseContainerRequestMutex.Lock()
	ret, specificReturn := fake.parseContainerRequestReturnsOnCall[len(fake.parseContainerRequestArgsForCall)]
	fake.parseContainerRequestArgsForCall = append(fake.parseContainerRequestArgsForCall, struct {
		arg1 interface{}
	}{arg1})
	stub := fake.ParseContainerRequestStub
	fakeReturns := fake.parseContainerRequestReturns
	fake.recordInvocation("ParseContainerRequest", []interface{}{arg1})
	fake.parseContainerRequestMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDispatcher) ParseContainerRequestCallCount() int {
	fake.parseContainerRequestMutex.RLock()
	defer fake.parseContainerRequestMutex.RUnlock()
	return len(fake.parseContainerRequestArgsForCall)
}

func (fake *FakeDispatcher) ParseContainerRequestCalls(stub func(interface{}) interface{}) {
	fake.parseContainerRequestMutex.Lock()
	defer fake.parseContainerRequestMutex.Unlock()
	fake.ParseContainerRequestStub = stub
}

func (fake *FakeDispatcher) ParseContainerRequestArgsForCall(i int) interface{} {
	fake.parseContainerRequestMutex.RLock()
	defer fake.parseContainerRequestMutex.RUnlock()
	argsForCall := fake.parseContainerRequestArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeDispatcher) ParseContainerRequestReturns(result1 interface{}) {
	fake.parseContainerRequestMutex.Lock()
	defer fake.parseContainerRequestMutex.Unlock()
	fake.ParseContainerRequestStub = nil
	fake.parseContainerRequestReturns = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) ParseContainerRequestReturnsOnCall(i int, result1 interface{}) {
	fake.parseContainerRequestMutex.Lock()
	defer fake.parseContainerRequestMutex.Unlock()
	fake.ParseContainerRequestStub = nil
	if fake.parseContainerRequestReturnsOnCall == nil {
		fake.parseContainerRequestReturnsOnCall = make(map[int]struct {
			result1 interface{}
		})
	}
	fake.parseContainerRequestReturnsOnCall[i] = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) ParsePodRequest(arg1 interface{}) interface{} {
	fake.parsePodRequestMutex.Lock()
	ret, specificReturn := fake.parsePodRequestReturnsOnCall[len(fake.parsePodRequestArgsForCall)]
	fake.parsePodRequestArgsForCall = append(fake.parsePodRequestArgsForCall, struct {
		arg1 interface{}
	}{arg1})
	stub := fake.ParsePodRequestStub
	fakeReturns := fake.parsePodRequestReturns
	fake.recordInvocation("ParsePodRequest", []interface{}{arg1})
	fake.parsePodRequestMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDispatcher) ParsePodRequestCallCount() int {
	fake.parsePodRequestMutex.RLock()
	defer fake.parsePodRequestMutex.RUnlock()
	return len(fake.parsePodRequestArgsForCall)
}

func (fake *FakeDispatcher) ParsePodRequestCalls(stub func(interface{}) interface{}) {
	fake.parsePodRequestMutex.Lock()
	defer fake.parsePodRequestMutex.Unlock()
	fake.ParsePodRequestStub = stub
}

func (fake *FakeDispatcher) ParsePodRequestArgsForCall(i int) interface{} {
	fake.parsePodRequestMutex.RLock()
	defer fake.parsePodRequestMutex.RUnlock()
	argsForCall := fake.parsePodRequestArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeDispatcher) ParsePodRequestReturns(result1 interface{}) {
	fake.parsePodRequestMutex.Lock()
	defer fake.parsePodRequestMutex.Unlock()
	fake.ParsePodRequestStub = nil
	fake.parsePodRequestReturns = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) ParsePodRequestReturnsOnCall(i int, result1 interface{}) {
	fake.parsePodRequestMutex.Lock()
	defer fake.parsePodRequestMutex.Unlock()
	fake.ParsePodRequestStub = nil
	if fake.parsePodRequestReturnsOnCall == nil {
		fake.parsePodRequestReturnsOnCall = make(map[int]struct {
			result1 interface{}
		})
	}
	fake.parsePodRequestReturnsOnCall[i] = struct {
		result1 interface{}
	}{result1}
}

func (fake *FakeDispatcher) SetDockerCgroupDriver(arg1 string) {
	fake.setDockerCgroupDriverMutex.Lock()
	fake.setDockerCgroupDriverArgsForCall = append(fake.setDockerCgroupDriverArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.SetDockerCgroupDriverStub
	fake.recordInvocation("SetDockerCgroupDriver", []interface{}{arg1})
	fake.setDockerCgroupDriverMutex.Unlock()
	if stub != nil {
		fake.SetDockerCgroupDriverStub(arg1)
	}
}

func (fake *FakeDispatcher) SetDockerCgroupDriverCallCount() int {
	fake.setDockerCgroupDriverMutex.RLock()
	defer fake.setDockerCgroupDriverMutex.RUnlock()
	return len(fake.setDockerCgroupDriverArgsForCall)
}

func (fake *FakeDispatcher) SetDockerCgroupDriverCalls(stub func(string)) {
	fake.setDockerCgroupDriverMutex.Lock()
	defer fake.setDockerCgroupDriverMutex.Unlock()
	fake.SetDockerCgroupDriverStub = stub
}

func (fake *FakeDispatcher) SetDockerCgroupDriverArgsForCall(i int) string {
	fake.setDockerCgroupDriverMutex.RLock()
	defer fake.setDockerCgroupDriverMutex.RUnlock()
	argsForCall := fake.setDockerCgroupDriverArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeDispatcher) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.backfillRequestMutex.RLock()
	defer fake.backfillRequestMutex.RUnlock()
	fake.deleteFromCacheIfNeedMutex.RLock()
	defer fake.deleteFromCacheIfNeedMutex.RUnlock()
	fake.dispatchMutex.RLock()
	defer fake.dispatchMutex.RUnlock()
	fake.insertIntoCacheIfNeedMutex.RLock()
	defer fake.insertIntoCacheIfNeedMutex.RUnlock()
	fake.interceptRuntimeRequestMutex.RLock()
	defer fake.interceptRuntimeRequestMutex.RUnlock()
	fake.parseContainerRequestMutex.RLock()
	defer fake.parseContainerRequestMutex.RUnlock()
	fake.parsePodRequestMutex.RLock()
	defer fake.parsePodRequestMutex.RUnlock()
	fake.setDockerCgroupDriverMutex.RLock()
	defer fake.setDockerCgroupDriverMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeDispatcher) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ dispatcher.Dispatcher = new(FakeDispatcher)
