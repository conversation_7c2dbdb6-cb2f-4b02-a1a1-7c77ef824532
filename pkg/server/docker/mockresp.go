/*
 * Copyright (c) 2025 Huawei Technology corp.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package docker

import (
	"bufio"
	"errors"
	"io"
	"net"
	"net/http"
)

type mockRespWriter struct {
	http.ResponseWriter
	w    io.Writer
	code int
}

func (m *mockRespWriter) Write(p []byte) (n int, err error) {
	m.w.Write(p)
	return m.ResponseWriter.Write(p)
}

func (m *mockRespWriter) WriteHeader(c int) {
	m.code = c
	m.ResponseWriter.WriteHeader(c)
}

func (m mockRespWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	h, ok := m.ResponseWriter.(http.Hijacker)
	if !ok {
		return nil, nil, errors.New("hijack not supported")
	}
	return h.Hijack()
}
