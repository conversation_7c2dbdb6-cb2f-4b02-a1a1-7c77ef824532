// To regenerate api.pb.go run hack/generate-runtime.sh
syntax = "proto3";

package runtime.v1alpha1;
option go_package = "kunpeng.huawei.com/tap/api/policy-manager/v1alpha1";


// PodSandboxMetadata holds all necessary information for sandbox.
message PodSandboxMetadata {
  // Pod name of the sandbox. Same as the pod name in the Pod ObjectMeta.
  string name = 1;
  // Pod UID of the sandbox. Same as the pod UID in the Pod ObjectMeta.
  string uid = 2;
  // Pod namespace of the sandbox. Same as the pod namespace in the Pod ObjectMeta.
  string namespace = 3;
  // Pod ID
	string id = 4;
  // Attempt number of creating the sandbox. Default: 0.
  uint32 attempt = 5;
}

// RunPodSandboxHookRequest is sent to RuntimeHookServer before pod creating request transferred to
// backend containerd or dockerd. This Request is generated basing on CRI's RunPodSandboxRequest, including pod
// Meta, Resources, Annotations .e.g and RuntimeHookServer should ensure correct operations basing on
// this request.
message PodSandboxHookRequest {
  // Metadata of the sandbox. This information will uniquely identify the sandbox.
  PodSandboxMetadata pod_meta = 1;
  // Named runtime to use for podSandbox.
  string runtime_handler = 2;
  // Labels/Annotations are key-value pairs that may be used.
  map<string, string> labels = 3;
  map<string, string> annotations = 4;
  // Parent cgroup of the pod sandbox.
  string cgroup_parent = 5;
  // Optional overhead represents the overheads associated with this sandbox,
  // same as LinuxPodSandboxConfig.overhead under CRI scenario
  LinuxContainerResources overhead = 6;
  // Optional resources represents the sum of container resources for this sandbox
  // same as LinuxPodSandboxConfig.resources under CRI scenario
  LinuxContainerResources resources = 7;
}

// RunPodSandboxHookResponse is RuntimeHookServer's response to RunPodSandboxHookRequest.
// RuntimeManager will merge RunPodSandboxHookResponse and RunPodSandboxRequest to generate a
// RunPodSandboxRequest to containerd(dockerd).
message PodSandboxHookResponse {
  // RuntimeHookServer may inject additional labels/annotations to PodSandboxRequest to be send to
  // backend contaienrd/dockerd
  map<string, string> labels = 1;
  map<string, string> annotations = 2;
  // RuntimeHookSever may modify cgroup_parent to construct customized cgroup topology.
  string cgroup_parent = 3;
  // RuntimeHookServer may modify the linux resource config.
  // Optional resources represents the sum of container resources for this sandbox
  LinuxContainerResources resources = 4;
}

// LinuxContainerResources specifies Linux specific configuration for
// resources.
message LinuxContainerResources {
  // CPU CFS (Completely Fair Scheduler) period. Default: 0 (not specified).
  int64 cpu_period = 1;
  // CPU CFS (Completely Fair Scheduler) quota. Default: 0 (not specified).
  int64 cpu_quota = 2;
  // CPU shares (relative weight vs. other containers). Default: 0 (not specified).
  int64 cpu_shares = 3;
  // Memory limit in bytes. Default: 0 (not specified).
  int64 memory_limit_in_bytes = 4;
  // OOMScoreAdj adjusts the oom-killer score. Default: 0 (not specified).
  int64 oom_score_adj = 5;
  // CpusetCpus constrains the allowed set of logical CPUs. Default: "" (not specified).
  string cpuset_cpus = 6;
  // CpusetMems constrains the allowed set of memory nodes. Default: "" (not specified).
  string cpuset_mems = 7;
  // List of HugepageLimits to limit the HugeTLB usage of container per page size. Default: nil (not specified).
  repeated HugepageLimit hugepage_limits = 8;
  // Unified resources for cgroup v2. Default: nil (not specified).
  // Each key/value in the map refers to the cgroup v2.
  // e.g. "memory.max": "6937202688" or "io.weight": "default 100".
  map<string, string> unified = 9;
  // Memory swap limit in bytes. Default 0 (not specified).
  int64 memory_swap_limit_in_bytes = 10;
}

// HugepageLimit corresponds to the file`hugetlb.<hugepagesize>.limit_in_byte` in container level cgroup.
// For example, `PageSize=1GB`, `Limit=1073741824` means setting `1073741824` bytes to hugetlb.1GB.limit_in_bytes.
message HugepageLimit {
  // The value of PageSize has the format <size><unit-prefix>B (2MB, 1GB),
  // and must match the <hugepagesize> of the corresponding control file found in `hugetlb.<hugepagesize>.limit_in_bytes`.
  // The values of <unit-prefix> are intended to be parsed using base 1024("1KB" = 1024, "1MB" = 1048576, etc).
  string page_size = 1;
  // limit in bytes of hugepagesize HugeTLB usage.
  uint64 limit = 2;
}

message ContainerMetadata {
  // Name of the container. Same as the container name in the PodSpec.
  string name = 1;
  // Attempt number of creating the container. Default: 0.
  uint32 attempt = 2;
  // Id of the container.
  string id = 3;
}

// ContainerResourceHookRequest is sent to RuntimeHookServer before/after container related operations including
// pre-container-start, post-container-create, pre-container-resource-update, post-container-stop.
message ContainerResourceHookRequest {
  PodSandboxMetadata pod_meta = 1;
  ContainerMetadata container_meta = 2;
  // container related annotations
  map<string, string> container_annotations = 3;
  LinuxContainerResources container_resources = 4;
  LinuxContainerResources pod_resources = 5;
  // pod related annotations and labels
  map<string, string> pod_annotations = 6;
  map<string, string> pod_labels = 7;
  string pod_cgroup_parent = 8;
  map<string, string> container_envs = 9;
  // TODO: add the error info from containerd/dockerd
}

// ContainerResourceHookResponse is RuntimeHookServer's response to ContainerResourceHookRequest.
// RuntimeManager will merge ContainerResourceHookResponse and Pre hookType Request to generate a
// RunPodSandboxRequest to containerd(dockerd).
message ContainerResourceHookResponse {
  map<string, string> container_annotations = 1;
  LinuxContainerResources container_resources = 2;
  string pod_cgroup_parent = 3;
  map<string, string> container_envs = 4;
}

// Runtime service defines the public APIs for talk between RuntimeHookServer and RuntimeManager
service RuntimeHookService {
  // PreRunPodSandboxHook calls RuntimeHookServer before pod creating, and would merge RunPodSandboxHookResponse
  // and Original RunPodSandboxRequest generating a new RunPodSandboxRequest to transfer to backend runtime engine.
  // RuntimeHookServer should ensure the correct operations basing on RunPodSandboxHookRequest.
  rpc PreRunPodSandboxHook(PodSandboxHookRequest) returns (PodSandboxHookResponse) {}
  // PostStopPodSandboxHook calls RuntimeHookServer after pod deleted. RuntimeHookServer could do resource setting garbage collection
  // sanity check after PodSandBox stopped.
  rpc PostStopPodSandboxHook(PodSandboxHookRequest) returns (PodSandboxHookResponse) {}
  // PreRemovePodSandboxHook calls RuntimeHookServer before podsandbox removed
  rpc PreRemovePodSandboxHook(PodSandboxHookRequest) returns (PodSandboxHookResponse) {}
  // PreCreateContainerHook calls RuntimeHookServer before container creating. RuntimeHookServer could do some
  // resource setting before container launching.
  rpc PreCreateContainerHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
  // PreStartContainerHook calls RuntimeHookServer before container starting, RuntimeHookServer could do some
  // resource adjustments before container launching.
  rpc PreStartContainerHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
  // PostStartContainerHook calls RuntimeHookServer after container starting. RuntimeHookServer could do resource
  // set checking after container launch.
  rpc PostStartContainerHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
  // PostStopContainerHook calls RuntimeHookServer after container stop. RuntimeHookServer could do resource setting
  // garbage collection.
  rpc PostStopContainerHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
  // PreUpdateContainerResourcesHook calls RuntimeHookServer before container resource update to keep resource policy
  // consistent
  rpc PreUpdateContainerResourcesHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
  // PreRemoveContainerHook calls RuntimeHookServer before container removed
  rpc PreRemoveContainerHook(ContainerResourceHookRequest) returns (ContainerResourceHookResponse) {}
}
