# Kunpeng TAP RPM 构建指南

本文档介绍如何使用 Makefile 中的命令来构建 kunpeng-tap 的 RPM 包。

## 前置要求

确保您的系统已安装以下软件：

- `rpmbuild` (通常在 `rpm-build` 包中)
- `golang` (版本 >= 1.20)
- `make`
- `git`

在 OpenEuler/CentOS/RHEL 系统上安装：

```bash
sudo yum install -y rpm-build golang make git
```

## 快速开始

### 1. 查看可用的 RPM 相关命令

```bash
make rpm-help
```

这将显示所有可用的 RPM 构建相关命令。

### 2. 构建 Docker 运行时版本的 RPM 包

```bash
make rpm-build-docker
```

### 3. 构建 Containerd 运行时版本的 RPM 包

```bash
make rpm-build-containerd
```

### 4. 完整的构建和验证流程

```bash
make rpm-all
```

## 详细步骤

### 步骤 1: 准备构建环境

```bash
make rpm-prepare
```

这将创建必要的 RPM 构建目录结构。

### 步骤 2: 构建二进制文件并创建源码包

```bash
make rpm-source
```

这将：
- 构建 `koord-runtime-proxy` 二进制文件
- 创建包含所有必要文件的源码 tarball
- 将 tarball 放置在 RPM 构建目录中

### 步骤 3: 复制 spec 文件

```bash
make rpm-spec
```

### 步骤 4: 构建 RPM 包

对于 Docker 运行时：
```bash
make rpm-build RUNTIME_TYPE=docker
```

对于 Containerd 运行时：
```bash
make rpm-build RUNTIME_TYPE=containerd
```

### 步骤 5: 验证构建的包

```bash
make rpm-verify
```

这将显示包的详细信息和内容列表。

### 步骤 6: 测试安装（干运行）

```bash
make rpm-test-install
```

这将测试包的安装过程，但不会实际安装。

## 安装和卸载

### 安装 RPM 包

```bash
make rpm-install
```

### 卸载 RPM 包

```bash
make rpm-uninstall
```

## 清理构建文件

```bash
make rpm-clean
```

这将删除所有 RPM 构建相关的临时文件和目录。

## 查看包信息

```bash
make rpm-info
```

这将显示当前配置的包信息，包括版本、架构等。

## 自定义构建参数

您可以通过设置环境变量来自定义构建参数：

```bash
# 自定义版本号
make rpm-build RPM_VERSION=1.2.3

# 自定义发布号
make rpm-build RPM_RELEASE=2

# 自定义运行时类型
make rpm-build RUNTIME_TYPE=containerd

# 组合使用
make rpm-build RPM_VERSION=1.2.3 RPM_RELEASE=2 RUNTIME_TYPE=containerd
```

## 构建输出

成功构建后，RPM 包将位于：
```
rpmbuild/RPMS/aarch64/kunpeng-tap-<version>-<release>.aarch64.rpm
```

## 故障排除

### 构建失败

1. 检查是否安装了所有必需的依赖
2. 确保 Go 版本 >= 1.20
3. 检查网络连接（构建过程中可能需要下载 Go 模块）

### 权限问题

如果在安装过程中遇到权限问题，请确保使用 `sudo` 运行安装命令：

```bash
sudo make rpm-install
```

### 清理并重新构建

如果遇到问题，可以清理所有构建文件并重新开始：

```bash
make rpm-clean
make rpm-all
```

## 包内容

构建的 RPM 包包含：

- `/usr/local/bin/kunpeng-tap` - 主要的二进制文件
- `/etc/systemd/system/kunpeng-tap.service` - systemd 服务文件
- `/var/run/kunpeng/` - 运行时目录

安装后，服务将自动启用但不会自动启动。您可以使用以下命令启动服务：

```bash
sudo systemctl start kunpeng-tap.service
sudo systemctl status kunpeng-tap.service
```
